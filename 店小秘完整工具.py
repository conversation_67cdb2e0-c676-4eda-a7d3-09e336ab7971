import requests
import json
import time
import configparser
import os
import re
from typing import Dict, Any, Optional, List


class DianXiaoMiTool:
    """店小秘完整工具 - 整合商品ID提取、测试和发布功能"""

    def __init__(self, config_file: str = "配置.ini"):
        self.config_file = config_file
        self.config = self.load_config()
        
        # 从配置文件获取Cookie
        cookie = self.config.get('认证信息', 'cookie', fallback='')
        
        self.base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/x-www-form-urlencoded',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'referrer': 'https://www.dianxiaomi.com/web/popTemu/pageList/offline?dxmOfflineState=publishFail',
            'referrerPolicy': 'strict-origin-when-cross-origin',
            'Cookie': cookie,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 从配置文件获取设置
        self.step_delay = self.config.getint('请求设置', 'step_delay', fallback=2)
        self.check_delay = self.config.getint('请求设置', 'check_delay', fallback=3)
        self.max_check_attempts = self.config.getint('请求设置', 'max_check_attempts', fallback=10)
        
        # 商品ID提取相关设置
        self.extract_base_url = "https://www.dianxiaomi.com/api/popTemuProduct/pageList.json"
        self.extract_base_data = {
            'sortName': '2',
            'pageSize': '300',
            'searchType': '0',
            'searchValue': '',
            'productSearchType': '1',
            'shopId': '-1',
            'dxmState': 'offline',
            'dxmOfflineState': 'publishFail',
            'site': '0',
            'fullCid': '',
            'sortValue': '2',
            'productType': ''
        }
        
        # 统计信息
        self.all_product_ids = []
        self.success_count = 0
        self.failed_count = 0
        self.failed_products = []

    def load_config(self) -> configparser.ConfigParser:
        """加载配置文件"""
        config = configparser.ConfigParser(interpolation=None)
        
        if not os.path.exists(self.config_file):
            print(f"⚠️  配置文件 {self.config_file} 不存在，创建默认配置文件")
            self.create_default_config()
        
        try:
            config.read(self.config_file, encoding='utf-8')
            print(f"✅ 成功加载配置文件: {self.config_file}")
            return config
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            raise

    def create_default_config(self):
        """创建默认配置文件"""
        config = configparser.ConfigParser(interpolation=None)
        
        config.add_section('认证信息')
        config.set('认证信息', 'cookie', '请在此处填入您的Cookie')
        
        config.add_section('请求设置')
        config.set('请求设置', 'step_delay', '2')
        config.set('请求设置', 'check_delay', '3')
        config.set('请求设置', 'max_check_attempts', '10')
        
        config.add_section('测试设置')
        config.set('测试设置', 'default_product_id', '138220088856533368')
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            print("请编辑配置文件，填入正确的Cookie后重新运行程序")
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")

    def update_cookie(self):
        """更新配置文件中的Cookie"""
        current_cookie = self.config.get('认证信息', 'cookie', fallback='')
        if current_cookie and current_cookie != '请在此处填入您的Cookie':
            print(f"\n当前Cookie: {current_cookie[:100]}...（已截断）")
        else:
            print("\n当前Cookie: 未设置")
        
        print("\n请粘贴新的Cookie字符串:")
        new_cookie = input().strip()
        
        if not new_cookie:
            print("❌ Cookie为空，取消更新")
            return False
        
        # 更新配置
        if '认证信息' not in self.config:
            self.config.add_section('认证信息')
        
        self.config.set('认证信息', 'cookie', new_cookie)
        
        # 保存配置文件
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            print(f"✅ Cookie已更新并保存到 {self.config_file}")
            print(f"新Cookie: {new_cookie[:100]}...（已截断）")
            
            # 更新headers中的Cookie
            self.base_headers['Cookie'] = new_cookie
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False

    # ==================== 商品ID提取功能 ====================
    
    def request_page(self, page_no: int) -> Dict[str, Any]:
        """请求指定页面的数据"""
        data = self.extract_base_data.copy()
        data['pageNo'] = str(page_no)
        
        try:
            print(f"正在请求第 {page_no} 页...")
            response = requests.post(self.extract_base_url, headers=self.base_headers, data=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return {}
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return {}

    def extract_ids_from_page(self, json_data: Dict[str, Any]) -> List[str]:
        """从页面数据中提取商品ID"""
        product_ids = []
        
        if 'data' in json_data and 'page' in json_data['data'] and 'list' in json_data['data']['page']:
            product_list = json_data['data']['page']['list']
            
            for product in product_list:
                if 'id' in product:
                    product_ids.append(str(product['id']))
        
        return product_ids

    def extract_all_product_ids(self) -> List[str]:
        """提取所有页面的商品ID"""
        print("开始提取所有商品ID...")
        
        self.all_product_ids = []
        page_no = 1
        max_pages = 1000
        page_size = int(self.extract_base_data['pageSize'])
        
        while page_no <= max_pages:
            if page_no > 1:
                time.sleep(1)  # 请求间隔
            
            page_data = self.request_page(page_no)
            if not page_data:
                print(f"第 {page_no} 页请求失败，停止提取")
                break
            
            page_ids = self.extract_ids_from_page(page_data)
            
            if not page_ids:
                print(f"第 {page_no} 页没有商品数据，已到最后一页")
                break
            
            self.all_product_ids.extend(page_ids)
            print(f"第 {page_no} 页提取到 {len(page_ids)} 个商品ID")
            
            if len(page_ids) < page_size:
                print(f"第 {page_no} 页商品数量({len(page_ids)})少于页面大小({page_size})，已到最后一页")
                break
            
            page_no += 1
        
        if page_no > max_pages:
            print(f"已达到最大页数限制({max_pages})，停止提取")
        
        return self.all_product_ids

    def save_product_ids_to_file(self, filename: str = "商品ID列表.txt"):
        """保存商品ID到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                for i, product_id in enumerate(self.all_product_ids, 1):
                    f.write(f"{i}. {product_id}\n")
            print(f"商品ID已保存到文件: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")

    def print_extract_summary(self):
        """打印提取结果摘要"""
        print("\n" + "="*50)
        print("提取完成！")
        print(f"总共提取到 {len(self.all_product_ids)} 个商品ID")
        
        if self.all_product_ids:
            print("\n前10个商品ID:")
            for i, product_id in enumerate(self.all_product_ids[:10], 1):
                print(f"{i}. {product_id}")
            
            if len(self.all_product_ids) > 10:
                print("...")
                print(f"最后一个商品ID: {self.all_product_ids[-1]}")
        
        print("="*50)

    # ==================== 测试功能 ====================
    
    def print_separator(self, title: str):
        """打印分隔线"""
        print(f"\n{'='*80}")
        print(f"  {title}")
        print(f"{'='*80}")

    def print_request_info(self, step: str, url: str, data: Dict[str, str]):
        """打印请求信息"""
        print(f"\n📤 {step} - 请求信息:")
        print(f"URL: {url}")
        print(f"Method: POST")
        print(f"Headers:")
        for key, value in self.base_headers.items():
            if key == 'Cookie':
                print(f"  {key}: {value[:100]}...（已截断）")
            else:
                print(f"  {key}: {value}")
        print(f"Data:")
        for key, value in data.items():
            print(f"  {key}: {value}")

    def print_response_info(self, step: str, response: requests.Response):
        """打印响应信息"""
        print(f"\n📥 {step} - 响应信息:")
        print(f"状态码: {response.status_code}")
        print(f"响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print(f"响应体:")
        try:
            json_data = response.json()
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except:
            print(response.text)

    def make_detailed_request(self, step: str, url: str, data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """发送详细的HTTP请求"""
        self.print_request_info(step, url, data)
        
        try:
            response = requests.post(url, headers=self.base_headers, data=data)
            self.print_response_info(step, response)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"\n❌ {step} 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"\n❌ {step} 请求异常: {e}")
            return None

    def make_request(self, url: str, data: Dict[str, str], debug: bool = False) -> Optional[Dict[str, Any]]:
        """发送HTTP请求"""
        try:
            if debug:
                print(f"    请求URL: {url}")
                print(f"    请求数据: {data}")

            response = requests.post(url, headers=self.base_headers, data=data)

            if debug:
                print(f"    响应状态码: {response.status_code}")
                print(f"    响应内容: {response.text[:500]}...")

            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
        except Exception as e:
            print(f"请求异常: {e}")
            return None

    # ==================== 测试步骤功能 ====================

    def test_step1_price_detection(self, product_id: str, detailed: bool = False) -> bool:
        """测试步骤1: 价格保护检测"""
        if detailed:
            self.print_separator("步骤1: 价格保护检测")
        else:
            print(f"  步骤1: 价格保护检测...")

        url = "https://www.dianxiaomi.com/api/priceDetection/batchProtectPriceDetection.json"
        data = {
            'dxmState': 'offline',
            'platform': 'popTemu',
            'ids': product_id
        }

        if detailed:
            result = self.make_detailed_request("步骤1", url, data)
        else:
            result = self.make_request(url, data)

        if result and result.get('code') == 0:
            if detailed:
                print(f"\n✅ 步骤1 成功")
            else:
                print(f"  ✅ 价格保护检测通过")
            return True
        else:
            if detailed:
                print(f"\n❌ 步骤1 失败")
                if result:
                    error_code = result.get('code')
                    error_msg = result.get('msg')
                    print(f"错误代码: {error_code}")
                    print(f"错误信息: {error_msg}")

                    if error_code == 2001:
                        print("💡 分析: Cookie验证失败，可能是Cookie过期或无效")
                    elif error_code == 403:
                        print("💡 分析: 权限不足，请检查账号权限")
                    elif error_code == 404:
                        print("💡 分析: 商品ID不存在或已删除")
            else:
                print(f"  ❌ 价格保护检测失败: {result}")
                if result and result.get('code') == 2001:
                    print(f"  💡 提示: 可能是Cookie过期或权限不足，请更新Cookie")
            return False

    def test_step2_banned_word_check(self, product_id: str, detailed: bool = False) -> bool:
        """测试步骤2: 违禁词检查"""
        if detailed:
            self.print_separator("步骤2: 违禁词检查")
        else:
            print(f"  步骤2: 违禁词检查...")

        url = "https://www.dianxiaomi.com/api/bannedWord/batchCheckForBannedWord.json"
        data = {
            'productIds': product_id,
            'platform': 'popTemu'
        }

        if detailed:
            result = self.make_detailed_request("步骤2", url, data)
        else:
            result = self.make_request(url, data)

        if result and result.get('code') == 0:
            if detailed:
                print(f"\n✅ 步骤2 成功")
            else:
                print(f"  ✅ 违禁词检查通过")
            return True
        else:
            if detailed:
                print(f"\n❌ 步骤2 失败")
            else:
                print(f"  ❌ 违禁词检查失败: {result}")
            return False

    def test_step3_product_info_check(self, product_id: str, detailed: bool = False) -> Optional[str]:
        """测试步骤3: 商品信息检查"""
        if detailed:
            self.print_separator("步骤3: 商品信息检查")
        else:
            print(f"  步骤3: 商品信息检查...")

        url = "https://www.dianxiaomi.com/api/productCheck/batchCheckProductInfo.json"
        data = {
            'ids': product_id,
            'platform': 'popTemu'
        }

        if detailed:
            result = self.make_detailed_request("步骤3", url, data)
        else:
            result = self.make_request(url, data)

        if result and result.get('code') == 0:
            # data字段可能直接是UUID字符串，也可能是包含uuid字段的对象
            data_field = result.get('data')
            if isinstance(data_field, str):
                # data直接是UUID字符串
                uuid = data_field
                if detailed:
                    print(f"\n✅ 步骤3 成功，获得UUID: {uuid}")
                else:
                    print(f"  ✅ 商品信息检查启动，UUID: {uuid}")
                return uuid
            elif isinstance(data_field, dict) and 'uuid' in data_field:
                # data是包含uuid字段的对象
                uuid = data_field.get('uuid')
                if detailed:
                    print(f"\n✅ 步骤3 成功，获得UUID: {uuid}")
                else:
                    print(f"  ✅ 商品信息检查启动，UUID: {uuid}")
                return uuid
            else:
                if detailed:
                    print(f"\n✅ 步骤3 成功，但未获取到UUID（可能不需要）")
                else:
                    print(f"  ✅ 商品信息检查通过（无需UUID）")
                return "no_uuid_needed"  # 返回特殊值表示成功但无UUID
        else:
            if detailed:
                print(f"\n❌ 步骤3 失败")
            else:
                print(f"  ❌ 商品信息检查失败: {result}")
            return None

    def test_step4_check_process(self, uuid: str, detailed: bool = False) -> bool:
        """测试步骤4: 检查进度"""
        if detailed:
            self.print_separator("步骤4: 检查进度")
        else:
            print(f"  步骤4: 检查进度...")

        url = "https://www.dianxiaomi.com/api/checkProcess.json"

        for attempt in range(self.max_check_attempts):
            data = {'uuid': uuid}

            if detailed:
                result = self.make_detailed_request("步骤4", url, data)
            else:
                result = self.make_request(url, data)

            if result and result.get('code') == 0:
                data_field = result.get('data')

                # 检查data的类型和内容
                if isinstance(data_field, dict) and 'status' in data_field:
                    status = data_field.get('status')
                    if detailed:
                        print(f"\n检查状态: {status}")

                    if status == 'completed':
                        if detailed:
                            print(f"✅ 步骤4 成功")
                        else:
                            print(f"  ✅ 检查完成")
                        return True
                    elif status == 'failed':
                        if detailed:
                            print(f"❌ 步骤4 失败")
                        else:
                            print(f"  ❌ 检查失败")
                        return False
                    else:
                        if detailed:
                            print(f"⏳ 步骤4 进行中")
                        else:
                            print(f"  ⏳ 检查中... (尝试 {attempt + 1}/{self.max_check_attempts})")
                        if attempt < self.max_check_attempts - 1:
                            time.sleep(self.check_delay)
                        continue
                else:
                    # 如果data不是预期的格式，但请求成功，也认为是成功的
                    if detailed:
                        print(f"\n✅ 步骤4 成功（数据格式: {type(data_field).__name__}）")
                        print(f"数据内容: {data_field}")
                    else:
                        print(f"  ✅ 检查完成")
                    return True
            else:
                if detailed:
                    print(f"\n❌ 步骤4 失败")
                else:
                    print(f"  ❌ 检查进度请求失败: {result}")
                return False

        if detailed:
            print(f"\n❌ 步骤4 超时")
        else:
            print(f"  ❌ 检查超时")
        return False

    def test_step5_batch_publish(self, product_id: str, detailed: bool = False) -> bool:
        """测试步骤5: 批量发布"""
        if detailed:
            self.print_separator("步骤5: 批量发布")
        else:
            print(f"  步骤5: 批量发布...")

        url = "https://www.dianxiaomi.com/api/popTemuProduct/batchPublish3f8c2a97.json"
        data = {
            'ids': product_id,
            'shopId': '-1'
        }

        if detailed:
            result = self.make_detailed_request("步骤5", url, data)
        else:
            result = self.make_request(url, data)

        if result and result.get('code') == 0:
            if detailed:
                print(f"\n✅ 步骤5 成功")
            else:
                print(f"  ✅ 发布成功")
            return True
        else:
            if detailed:
                print(f"\n❌ 步骤5 失败")
            else:
                print(f"  ❌ 发布失败: {result}")
            return False

    # ==================== 完整流程功能 ====================

    def test_complete_flow(self, product_id: str, stop_on_error: bool = True):
        """测试完整发布流程"""
        print(f"\n🚀 开始测试商品发布流程")
        print(f"商品ID: {product_id}")
        print(f"停止策略: {'遇到错误停止' if stop_on_error else '继续执行所有步骤'}")

        steps_results = []

        # 步骤1: 价格保护检测
        step1_success = self.test_step1_price_detection(product_id, detailed=True)
        steps_results.append(("步骤1: 价格保护检测", step1_success))

        if not step1_success and stop_on_error:
            self.print_final_summary(steps_results)
            return

        time.sleep(self.step_delay)

        # 步骤2: 违禁词检查
        step2_success = self.test_step2_banned_word_check(product_id, detailed=True)
        steps_results.append(("步骤2: 违禁词检查", step2_success))

        if not step2_success and stop_on_error:
            self.print_final_summary(steps_results)
            return

        time.sleep(self.step_delay)

        # 步骤3: 商品信息检查
        uuid = self.test_step3_product_info_check(product_id, detailed=True)
        step3_success = uuid is not None
        steps_results.append(("步骤3: 商品信息检查", step3_success))

        if not step3_success and stop_on_error:
            self.print_final_summary(steps_results)
            return

        if uuid and uuid != "no_uuid_needed":
            time.sleep(self.step_delay)

            # 步骤4: 检查进度
            step4_success = self.test_step4_check_process(uuid, detailed=True)
            steps_results.append(("步骤4: 检查进度", step4_success))

            if not step4_success and stop_on_error:
                self.print_final_summary(steps_results)
                return
        elif uuid == "no_uuid_needed":
            print("\n⏭️  跳过步骤4（无需检查进度）")

        time.sleep(self.step_delay)

        # 步骤5: 批量发布
        step5_success = self.test_step5_batch_publish(product_id, detailed=True)
        steps_results.append(("步骤5: 批量发布", step5_success))

        self.print_final_summary(steps_results)

    def print_final_summary(self, steps_results):
        """打印最终总结"""
        self.print_separator("测试结果总结")

        success_count = 0
        for step_name, success in steps_results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{step_name}: {status}")
            if success:
                success_count += 1

        print(f"\n总体结果: {success_count}/{len(steps_results)} 步骤成功")

        if success_count == len(steps_results):
            print("🎉 所有步骤都成功！可以进行批量发布")
        else:
            print("⚠️  存在失败步骤，请检查上述详细信息")

    # ==================== 发布功能 ====================

    def publish_single_product(self, product_id: str) -> bool:
        """发布单个商品"""
        print(f"\n🚀 开始发布商品: {product_id}")

        # 步骤1: 价格保护检测
        if not self.test_step1_price_detection(product_id):
            return False
        time.sleep(self.step_delay)

        # 步骤2: 违禁词检查
        if not self.test_step2_banned_word_check(product_id):
            return False
        time.sleep(self.step_delay)

        # 步骤3: 商品信息检查
        uuid = self.test_step3_product_info_check(product_id)
        if not uuid:
            return False
        time.sleep(self.step_delay)

        # 步骤4: 检查进度（如果需要）
        if uuid != "no_uuid_needed":
            if not self.test_step4_check_process(uuid):
                return False
            time.sleep(self.step_delay)

        # 步骤5: 批量发布
        if not self.test_step5_batch_publish(product_id):
            return False

        print(f"✅ 商品 {product_id} 发布成功！")
        return True

    def publish_products_from_file(self, filename: str = "商品ID列表.txt"):
        """从文件读取商品ID并批量发布"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            product_ids = []
            for line in lines:
                line = line.strip()
                if line:
                    # 提取ID（格式：1. 138220088856533368）
                    match = re.search(r'\d+\.\s*(\d+)', line)
                    if match:
                        product_ids.append(match.group(1))

            print(f"从文件 {filename} 读取到 {len(product_ids)} 个商品ID")
            return self.publish_products(product_ids)

        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return False
        except Exception as e:
            print(f"读取文件失败: {e}")
            return False

    def publish_products(self, product_ids: List[str]) -> bool:
        """批量发布商品"""
        print(f"开始批量发布 {len(product_ids)} 个商品...")

        self.success_count = 0
        self.failed_count = 0
        self.failed_products = []

        for i, product_id in enumerate(product_ids, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(product_ids)}")

            if self.publish_single_product(product_id):
                self.success_count += 1
            else:
                self.failed_count += 1
                self.failed_products.append(product_id)

            # 商品间隔时间
            if i < len(product_ids):
                print(f"等待 {self.step_delay} 秒后处理下一个商品...")
                time.sleep(self.step_delay)

        self.print_publish_summary()
        return self.failed_count == 0

    def print_publish_summary(self):
        """打印发布结果摘要"""
        print(f"\n{'='*60}")
        print("发布完成！")
        print(f"成功: {self.success_count} 个")
        print(f"失败: {self.failed_count} 个")

        if self.failed_products:
            print(f"\n失败的商品ID:")
            for product_id in self.failed_products:
                print(f"  - {product_id}")

        print(f"{'='*60}")

    # ==================== 主菜单功能 ====================

    def show_main_menu(self):
        """显示主菜单"""
        print("\n🛠️  店小秘完整工具")
        print("="*50)
        print("1. 提取所有商品ID")
        print("2. 更新Cookie配置")
        print("3. 测试单个商品发布")
        print("4. 批量发布商品")
        print("5. 完整流程（提取+发布）")
        print("0. 退出程序")
        print("="*50)

    def run_extract_product_ids(self):
        """运行商品ID提取"""
        print("\n📦 开始提取商品ID...")

        # 检查Cookie是否有效
        cookie = self.config.get('认证信息', 'cookie', fallback='')
        if not cookie or cookie == '请在此处填入您的Cookie':
            print("❌ Cookie未配置，请先更新Cookie")
            return

        all_ids = self.extract_all_product_ids()

        if all_ids:
            self.print_extract_summary()
            self.save_product_ids_to_file()
            print(f"✅ 成功提取 {len(all_ids)} 个商品ID并保存到文件")
        else:
            print("❌ 未提取到任何商品ID，请检查Cookie或网络连接")

    def run_test_single_product(self):
        """运行单商品测试"""
        print("\n🧪 单商品发布测试")

        # 检查Cookie是否有效
        cookie = self.config.get('认证信息', 'cookie', fallback='')
        if not cookie or cookie == '请在此处填入您的Cookie':
            print("❌ Cookie未配置，请先更新Cookie")
            return

        # 从配置文件获取默认商品ID
        default_product_id = self.config.get('测试设置', 'default_product_id', fallback='138220088856533368')

        # 获取测试商品ID
        product_id = input(f"请输入要测试的商品ID (默认: {default_product_id}): ").strip()
        if not product_id:
            product_id = default_product_id

        # 显示当前配置信息
        print(f"\n📋 当前配置:")
        print(f"配置文件: {self.config_file}")
        print(f"步骤延时: {self.step_delay}秒")
        print(f"检查延时: {self.check_delay}秒")
        print(f"最大检查次数: {self.max_check_attempts}")

        # 询问测试模式
        print("\n选择测试模式:")
        print("1. 遇到错误立即停止（推荐）")
        print("2. 执行所有步骤（无论是否出错）")

        mode = input("请选择 (1/2): ").strip()
        stop_on_error = mode != '2'

        # 开始测试
        self.test_complete_flow(product_id, stop_on_error)

    def run_batch_publish(self):
        """运行批量发布"""
        print("\n📦 批量发布模式")

        # 检查Cookie是否有效
        cookie = self.config.get('认证信息', 'cookie', fallback='')
        if not cookie or cookie == '请在此处填入您的Cookie':
            print("❌ Cookie未配置，请先更新Cookie")
            return

        # 检查商品ID文件是否存在
        filename = "商品ID列表.txt"
        if not os.path.exists(filename):
            print(f"❌ 文件 {filename} 不存在")
            print("请先运行选项1提取商品ID，或手动创建商品ID列表文件")
            return

        # 显示文件信息
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            product_count = len([line for line in lines if line.strip()])
            print(f"📋 找到商品ID文件: {filename}")
            print(f"📊 文件中包含 {product_count} 个商品ID")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return

        # 确认发布
        confirm = input(f"\n确认开始批量发布 {product_count} 个商品？(y/n): ")
        if confirm.lower() != 'y':
            print("已取消批量发布")
            return

        # 开始批量发布
        self.publish_products_from_file(filename)

    def run_complete_workflow(self):
        """运行完整工作流程（提取+发布）"""
        print("\n🔄 完整工作流程（提取商品ID + 批量发布）")

        # 检查Cookie是否有效
        cookie = self.config.get('认证信息', 'cookie', fallback='')
        if not cookie or cookie == '请在此处填入您的Cookie':
            print("❌ Cookie未配置，请先更新Cookie")
            return

        print("⚠️  警告：此操作将：")
        print("1. 提取所有商品ID（覆盖现有的商品ID列表文件）")
        print("2. 立即开始批量发布所有提取到的商品")
        print("3. 整个过程可能需要很长时间")

        confirm = input("\n确认执行完整工作流程？(y/n): ")
        if confirm.lower() != 'y':
            print("已取消完整工作流程")
            return

        # 步骤1: 提取商品ID
        print("\n" + "="*60)
        print("步骤1: 提取商品ID")
        print("="*60)

        all_ids = self.extract_all_product_ids()

        if not all_ids:
            print("❌ 未提取到任何商品ID，终止流程")
            return

        self.print_extract_summary()
        self.save_product_ids_to_file()

        # 询问是否继续发布
        print(f"\n✅ 成功提取 {len(all_ids)} 个商品ID")
        continue_publish = input("是否继续进行批量发布？(y/n): ")

        if continue_publish.lower() != 'y':
            print("已停止流程，商品ID已保存到文件")
            return

        # 步骤2: 批量发布
        print("\n" + "="*60)
        print("步骤2: 批量发布")
        print("="*60)

        self.publish_products(all_ids)

    def run(self):
        """运行主程序"""
        while True:
            try:
                self.show_main_menu()
                choice = input("请选择操作 (0-5): ").strip()

                if choice == '0':
                    print("👋 感谢使用店小秘完整工具，再见！")
                    break
                elif choice == '1':
                    self.run_extract_product_ids()
                elif choice == '2':
                    self.update_cookie()
                elif choice == '3':
                    self.run_test_single_product()
                elif choice == '4':
                    self.run_batch_publish()
                elif choice == '5':
                    self.run_complete_workflow()
                else:
                    print("❌ 无效选择，请输入 0-5 之间的数字")

                if choice != '0':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n❌ 程序运行出错: {e}")
                input("按回车键继续...")


def main():
    """主函数"""
    print("🛠️  店小秘完整工具")
    print("="*50)
    print("功能包括：")
    print("- 商品ID提取")
    print("- 单商品测试")
    print("- 批量发布")
    print("- Cookie配置管理")
    print("="*50)

    try:
        # 初始化工具
        tool = DianXiaoMiTool()

        # 检查配置文件
        cookie = tool.config.get('认证信息', 'cookie', fallback='')
        if not cookie or cookie == '请在此处填入您的Cookie':
            print("\n⚠️  首次使用提示：")
            print("检测到Cookie未配置，建议先更新Cookie后使用")
            print("您可以选择菜单中的选项2来更新Cookie")

        # 运行主程序
        tool.run()

    except FileNotFoundError:
        print("\n❌ 配置文件创建失败，请检查文件权限")
    except Exception as e:
        print(f"\n❌ 程序初始化失败: {e}")


if __name__ == "__main__":
    main()
