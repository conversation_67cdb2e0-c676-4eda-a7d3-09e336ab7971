import requests
import json
import time
from typing import List, Dict, Any


class ProductIdExtractor:
    """店小秘商品ID提取器"""
    
    def __init__(self):
        self.base_url = "https://www.dianxiaomi.com/api/popTemuProduct/pageList.json"
        self.headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': 'MYJ_MKTG_fapsc5t4tc=JTdCJTdE; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; _clck=e8utqr%7C2%7Cfx3%7C0%7C1916; _dxm_ad_client_id=A598CC49F8B80613EF428FD74B7439D2F; Hm_lvt_f8001a3f3d9bf5923f780580eb550c0b=**********,**********,**********,**********; HMACCOUNT=C15F99DB42A7BA7A; dxm_i=MjA4MzA1OSFhVDB5TURnek1EVTUhYzY4N2EzMWFmYWI1YTJhNTdhZjgwNWQ3MjdhYmY0NTk; dxm_t=********************************************************************************; dxm_c=ZTZCTTluZjghWXoxbE5rSk5PVzVtT0EhOTc1NjI5YjA2MTI1NjAxMGNhNTU3ZTcxNzExODI5YzU; dxm_w=MjZiM2JjZjIyMDhmMTQxMmJhMmRiMjVhNjNmYTI2MWEhZHoweU5tSXpZbU5tTWpJd09HWXhOREV5WW1FeVpHSXlOV0UyTTJaaE1qWXhZUSExM2JlYTk1NzRmNGY4N2IxNmNkNTk1YjJjMzdhNTgzMQ; dxm_s=1WgJuVor95ml7WCBI6mbhUQHhU5inFaADopIS6YaaIk; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; Hm_lpvt_f8001a3f3d9bf5923f780580eb550c0b=**********; JSESSIONID=710335BA095F715F7E5660C34A73701D',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.base_data = {
            'sortName': '2',
            'pageSize': '300',
            'searchType': '0',
            'searchValue': '',
            'productSearchType': '1',
            'shopId': '-1',
            'dxmState': 'offline',
            'dxmOfflineState': 'publishFail',
            'site': '0',
            'fullCid': '',
            'sortValue': '2',
            'productType': ''
        }
        self.all_product_ids = []
        self.request_delay = 1  # 请求间隔时间（秒）
    
    def request_page(self, page_no: int) -> Dict[str, Any]:
        """请求指定页面的数据"""
        data = self.base_data.copy()
        data['pageNo'] = str(page_no)
        
        try:
            print(f"正在请求第 {page_no} 页...")
            response = requests.post(self.base_url, headers=self.headers, data=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return {}
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return {}
    
    def extract_ids_from_page(self, json_data: Dict[str, Any]) -> List[str]:
        """从页面数据中提取商品ID"""
        product_ids = []
        
        if 'data' in json_data and 'page' in json_data['data'] and 'list' in json_data['data']['page']:
            product_list = json_data['data']['page']['list']
            
            for product in product_list:
                if 'id' in product:
                    product_ids.append(str(product['id']))
        
        return product_ids
    
    def get_total_pages(self, json_data: Dict[str, Any]) -> int:
        """获取总页数"""
        try:
            if 'data' in json_data and 'page' in json_data['data']:
                page_info = json_data['data']['page']
                total_count = page_info.get('totalCount', 0)
                page_size = page_info.get('pageSize', 300)
                total_pages = (total_count + page_size - 1) // page_size  # 向上取整
                return total_pages
        except Exception as e:
            print(f"获取总页数失败: {e}")
        
        return 0
    
    def extract_all_product_ids(self) -> List[str]:
        """提取所有页面的商品ID"""
        print("开始提取所有商品ID...")

        page_no = 1
        max_pages = 1000  # 设置最大页数防止无限循环
        page_size = int(self.base_data['pageSize'])

        while page_no <= max_pages:
            # 添加延时避免请求过快（第一页除外）
            if page_no > 1:
                time.sleep(self.request_delay)

            page_data = self.request_page(page_no)
            if not page_data:
                print(f"第 {page_no} 页请求失败，停止提取")
                break

            page_ids = self.extract_ids_from_page(page_data)

            if not page_ids:
                print(f"第 {page_no} 页没有商品数据，已到最后一页")
                break

            self.all_product_ids.extend(page_ids)
            print(f"第 {page_no} 页提取到 {len(page_ids)} 个商品ID")

            # 如果返回的商品数量少于页面大小，说明已经是最后一页
            if len(page_ids) < page_size:
                print(f"第 {page_no} 页商品数量({len(page_ids)})少于页面大小({page_size})，已到最后一页")
                break

            page_no += 1

        if page_no > max_pages:
            print(f"已达到最大页数限制({max_pages})，停止提取")

        return self.all_product_ids
    
    def save_to_file(self, filename: str = "商品ID列表.txt"):
        """保存商品ID到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                for i, product_id in enumerate(self.all_product_ids, 1):
                    f.write(f"{i}. {product_id}\n")
            print(f"商品ID已保存到文件: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")
    
    def print_summary(self):
        """打印提取结果摘要"""
        print("\n" + "="*50)
        print("提取完成！")
        print(f"总共提取到 {len(self.all_product_ids)} 个商品ID")
        
        if self.all_product_ids:
            print("\n前10个商品ID:")
            for i, product_id in enumerate(self.all_product_ids[:10], 1):
                print(f"{i}. {product_id}")
            
            if len(self.all_product_ids) > 10:
                print("...")
                print(f"最后一个商品ID: {self.all_product_ids[-1]}")
        
        print("="*50)


def main():
    """主函数"""
    extractor = ProductIdExtractor()
    
    # 提取所有商品ID
    all_ids = extractor.extract_all_product_ids()
    
    # 打印摘要
    extractor.print_summary()
    
    # 保存到文件
    if all_ids:
        extractor.save_to_file()
    
    return all_ids


if __name__ == "__main__":
    main()
