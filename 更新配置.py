import configparser
import os


def update_cookie():
    """更新配置文件中的Cookie"""
    config_file = "配置.ini"
    
    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return
    
    # 加载配置文件
    config = configparser.ConfigParser(interpolation=None)
    try:
        config.read(config_file, encoding='utf-8')
        print(f"✅ 成功加载配置文件: {config_file}")
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return
    
    # 显示当前Cookie（截断显示）
    current_cookie = config.get('认证信息', 'cookie', fallback='')
    if current_cookie:
        print(f"\n当前Cookie: {current_cookie[:100]}...（已截断）")
    else:
        print("\n当前Cookie: 未设置")
    
    # 获取新Cookie
    print("\n请粘贴新的Cookie字符串:")
    new_cookie = input().strip()
    
    if not new_cookie:
        print("❌ Cookie为空，取消更新")
        return
    
    # 更新配置
    if '认证信息' not in config:
        config.add_section('认证信息')
    
    config.set('认证信息', 'cookie', new_cookie)
    
    # 保存配置文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        print(f"✅ Cookie已更新并保存到 {config_file}")
        print(f"新Cookie: {new_cookie[:100]}...（已截断）")
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")


def main():
    """主函数"""
    print("🔧 配置更新工具")
    print("="*50)
    update_cookie()


if __name__ == "__main__":
    main()
