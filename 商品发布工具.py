import requests
import json
import time
import re
from typing import List, Dict, Any, Optional


class ProductPublisher:
    """店小秘商品发布工具"""

    def __init__(self, cookie: str = None):
        # 默认Cookie（需要用户更新）
        default_cookie = 'MYJ_MKTG_fapsc5t4tc=JTdCJTdE; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; _clck=e8utqr%7C2%7Cfx3%7C0%7C1916; _dxm_ad_client_id=A598CC49F8B80613EF428FD74B7439D2F; Hm_lvt_f8001a3f3d9bf5923f780580eb550c0b=**********,**********,**********,**********; HMACCOUNT=C15F99DB42A7BA7A; dxm_i=MjA4MzA1OSFhVDB5TURnek1EVTUhYzY4N2EzMWZmYWI1YTJhNTdhZjgwNWQ3MjdhYmY0NTk; dxm_t=********************************************************************************; dxm_c=ZTZCTTluZjghWXoxbE5rSk5PVzVtT0EhOTc1NjI5YjA2MTI1NjAxMGNhNTU3ZTcxNzExODI5YzU; dxm_w=MjZiM2JjZjIyMDhmMTQxMmJhMmRiMjVhNjNmYTI2MWEhZHoweU5tSXpZbU5tTWpJd09HWXhOREV5WW1FeVpHSXlOV0UyTTJaaE1qWXhZUSExM2JlYTk1NzRmNGY4N2IxNmNkNTk1YjJjMzdhNTgzMQ; dxm_s=1WgJuVor95ml7WCBI6mbhUQHhU5inFaADopIS6YaaIk; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; Hm_lpvt_f8001a3f3d9bf5923f780580eb550c0b=**********; JSESSIONID=710335BA095F715F7E5660C34A73701D'

        self.base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/x-www-form-urlencoded',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'referrer': 'https://www.dianxiaomi.com/web/popTemu/pageList/offline?dxmOfflineState=publishFail',
            'referrerPolicy': 'strict-origin-when-cross-origin',
            'Cookie': cookie if cookie else default_cookie,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def update_cookie(self, new_cookie: str):
        """更新Cookie"""
        self.base_headers['Cookie'] = new_cookie
        print("Cookie已更新")
        
        self.step_delay = 2  # 每步骤间隔时间（秒）
        self.check_delay = 3  # 检查进度间隔时间（秒）
        self.max_check_attempts = 10  # 最大检查次数
        
        # 统计信息
        self.success_count = 0
        self.failed_count = 0
        self.failed_products = []
    
    def make_request(self, url: str, data: Dict[str, str], debug: bool = False) -> Optional[Dict[str, Any]]:
        """发送HTTP请求"""
        try:
            if debug:
                print(f"    请求URL: {url}")
                print(f"    请求数据: {data}")

            response = requests.post(url, headers=self.base_headers, data=data)

            if debug:
                print(f"    响应状态码: {response.status_code}")
                print(f"    响应内容: {response.text[:500]}...")

            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
        except Exception as e:
            print(f"请求异常: {e}")
            return None
    
    def step1_price_detection(self, product_id: str, debug: bool = False) -> bool:
        """步骤1: 价格保护检测"""
        print(f"  步骤1: 价格保护检测...")
        url = "https://www.dianxiaomi.com/api/priceDetection/batchProtectPriceDetection.json"
        data = {
            'dxmState': 'offline',
            'platform': 'popTemu',
            'ids': product_id
        }

        result = self.make_request(url, data, debug)
        if result and result.get('success'):
            print(f"  ✅ 价格保护检测通过")
            return True
        else:
            print(f"  ❌ 价格保护检测失败: {result}")
            if result and result.get('code') == 2001:
                print(f"  💡 提示: 可能是Cookie过期或权限不足，请更新Cookie")
            return False
    
    def step2_banned_word_check(self, product_id: str) -> bool:
        """步骤2: 违禁词检查"""
        print(f"  步骤2: 违禁词检查...")
        url = "https://www.dianxiaomi.com/api/bannedWord/batchCheckForBannedWord.json"
        data = {
            'productIds': product_id,
            'platform': 'popTemu'
        }
        
        result = self.make_request(url, data)
        if result and result.get('success'):
            print(f"  ✅ 违禁词检查通过")
            return True
        else:
            print(f"  ❌ 违禁词检查失败: {result}")
            return False
    
    def step3_product_info_check(self, product_id: str) -> Optional[str]:
        """步骤3: 商品信息检查，返回UUID"""
        print(f"  步骤3: 商品信息检查...")
        url = "https://www.dianxiaomi.com/api/productCheck/batchCheckProductInfo.json"
        data = {
            'ids': product_id,
            'platform': 'popTemu'
        }
        
        result = self.make_request(url, data)
        if result and result.get('success'):
            # 从响应中提取UUID
            uuid = result.get('data', {}).get('uuid')
            if uuid:
                print(f"  ✅ 商品信息检查启动，UUID: {uuid}")
                return uuid
            else:
                print(f"  ❌ 未获取到检查UUID")
                return None
        else:
            print(f"  ❌ 商品信息检查失败: {result}")
            return None
    
    def step4_check_process(self, uuid: str) -> bool:
        """步骤4: 检查进度"""
        print(f"  步骤4: 检查进度...")
        url = "https://www.dianxiaomi.com/api/checkProcess.json"
        
        for attempt in range(self.max_check_attempts):
            data = {'uuid': uuid}
            result = self.make_request(url, data)
            
            if result and result.get('success'):
                status = result.get('data', {}).get('status')
                if status == 'completed':
                    print(f"  ✅ 检查完成")
                    return True
                elif status == 'failed':
                    print(f"  ❌ 检查失败")
                    return False
                else:
                    print(f"  ⏳ 检查中... (尝试 {attempt + 1}/{self.max_check_attempts})")
                    time.sleep(self.check_delay)
            else:
                print(f"  ❌ 检查进度请求失败: {result}")
                return False
        
        print(f"  ❌ 检查超时")
        return False
    
    def step5_batch_publish(self, product_id: str) -> bool:
        """步骤5: 批量发布"""
        print(f"  步骤5: 批量发布...")
        url = "https://www.dianxiaomi.com/api/popTemuProduct/batchPublish.json"
        data = {
            'ids': product_id,
            'shopId': '-1'
        }
        
        result = self.make_request(url, data)
        if result and result.get('success'):
            print(f"  ✅ 发布成功")
            return True
        else:
            print(f"  ❌ 发布失败: {result}")
            return False
    
    def publish_single_product(self, product_id: str) -> bool:
        """发布单个商品"""
        print(f"\n🚀 开始发布商品: {product_id}")
        
        # 步骤1: 价格保护检测
        if not self.step1_price_detection(product_id):
            return False
        time.sleep(self.step_delay)
        
        # 步骤2: 违禁词检查
        if not self.step2_banned_word_check(product_id):
            return False
        time.sleep(self.step_delay)
        
        # 步骤3: 商品信息检查
        uuid = self.step3_product_info_check(product_id)
        if not uuid:
            return False
        time.sleep(self.step_delay)
        
        # 步骤4: 检查进度
        if not self.step4_check_process(uuid):
            return False
        time.sleep(self.step_delay)
        
        # 步骤5: 批量发布
        if not self.step5_batch_publish(product_id):
            return False
        
        print(f"✅ 商品 {product_id} 发布成功！")
        return True
    
    def publish_products_from_file(self, filename: str = "商品ID列表.txt"):
        """从文件读取商品ID并批量发布"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            product_ids = []
            for line in lines:
                line = line.strip()
                if line:
                    # 提取ID（格式：1. 138220088856533368）
                    match = re.search(r'\d+\.\s*(\d+)', line)
                    if match:
                        product_ids.append(match.group(1))
            
            print(f"从文件 {filename} 读取到 {len(product_ids)} 个商品ID")
            return self.publish_products(product_ids)
            
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return False
        except Exception as e:
            print(f"读取文件失败: {e}")
            return False
    
    def publish_products(self, product_ids: List[str]) -> bool:
        """批量发布商品"""
        print(f"开始批量发布 {len(product_ids)} 个商品...")
        
        for i, product_id in enumerate(product_ids, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(product_ids)}")
            
            if self.publish_single_product(product_id):
                self.success_count += 1
            else:
                self.failed_count += 1
                self.failed_products.append(product_id)
            
            # 商品间隔时间
            if i < len(product_ids):
                print(f"等待 {self.step_delay} 秒后处理下一个商品...")
                time.sleep(self.step_delay)
        
        self.print_summary()
        return self.failed_count == 0
    
    def print_summary(self):
        """打印发布结果摘要"""
        print(f"\n{'='*60}")
        print("发布完成！")
        print(f"成功: {self.success_count} 个")
        print(f"失败: {self.failed_count} 个")
        
        if self.failed_products:
            print(f"\n失败的商品ID:")
            for product_id in self.failed_products:
                print(f"  - {product_id}")
        
        print(f"{'='*60}")


def main():
    """主函数"""
    print("🚀 店小秘商品发布工具")
    print("="*50)

    # 提示用户更新Cookie
    print("⚠️  重要提示：")
    print("1. 请确保您已登录店小秘网站")
    print("2. 请从浏览器开发者工具中复制最新的Cookie")
    print("3. 建议先测试单个商品，确认无误后再批量发布")
    print()

    # 询问是否更新Cookie
    update_cookie = input("是否需要更新Cookie？(y/n): ")

    if update_cookie.lower() == 'y':
        print("\n请粘贴完整的Cookie字符串:")
        new_cookie = input().strip()
        if new_cookie:
            publisher = ProductPublisher(cookie=new_cookie)
            print("✅ Cookie已更新")
        else:
            print("❌ Cookie为空，使用默认Cookie")
            publisher = ProductPublisher()
    else:
        publisher = ProductPublisher()

    print("\n选择操作模式:")
    print("1. 测试单个商品发布")
    print("2. 批量发布所有商品")

    choice = input("请选择 (1/2): ").strip()

    if choice == '1':
        # 测试模式
        test_product_id = "138220088856533368"
        print(f"\n🧪 测试模式 - 商品ID: {test_product_id}")
        success = publisher.step1_price_detection(test_product_id, debug=True)

        if success:
            print("\n✅ 测试成功！Cookie有效，可以进行批量发布")
        else:
            print("\n❌ 测试失败，请检查Cookie或网络连接")

    elif choice == '2':
        # 批量发布模式
        print(f"\n📦 批量发布模式")
        confirm = input("确认开始批量发布所有商品？(y/n): ")

        if confirm.lower() == 'y':
            publisher.publish_products_from_file("商品ID列表.txt")
        else:
            print("已取消批量发布")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
